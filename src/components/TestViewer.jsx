import { useEffect, useRef, useState } from 'react';

const TestViewer = ({ pdfUrl }) => {
  const containerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const container = containerRef.current;
    let cleanup = () => { };

    (async () => {
      console.log('TestViewer mounted');
      console.log('Container:', container);
      console.log('PDF URL:', pdfUrl);

      if (!container || !pdfUrl) {
        console.log('No container or PDF URL available');
        setError('No PDF URL provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const NutrientViewer = (await import('@nutrient-sdk/viewer')).default;
        console.log('NutrientViewer imported:', NutrientViewer);

        // Ensure there's only one NutrientViewer instance
        NutrientViewer.unload(container);

        const loadConfig = {
          container,
          document: pdfUrl,
          baseUrl: `${window.location.protocol}//${window.location.host}/`,
          // To remove "FOR EVALUATION PURPOSE ONLY" watermark, add your license key:
          // licenseKey: 'YOUR_LICENSE_KEY_HERE'
        };

        console.log('Load config:', loadConfig);

        const instance = await NutrientViewer.load(loadConfig);
        console.log('PDF loaded successfully:', instance);
        setIsLoading(false);

        cleanup = () => {
          NutrientViewer.unload(container);
        };
      } catch (error) {
        console.error('Error loading PDF:', error);
        console.error('Error stack:', error.stack);
        console.error('Error name:', error.name);
        setError(`Failed to load PDF: ${error.message}`);
        setIsLoading(false);
      }
    })();

    return cleanup;
  }, [pdfUrl]);

  if (!pdfUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-gray-600">No PDF selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="text-center">
            <svg className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <p className="text-gray-600">Loading PDF...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 z-10">
          <div className="text-center">
            <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-red-600 mb-2">Error loading PDF</p>
            <p className="text-sm text-red-500">{error}</p>
          </div>
        </div>
      )}

      <div
        ref={containerRef}
        className="w-full h-full"
        style={{
          width: '100vw',
          height: '100vh'
        }}
      />
    </div>
  );
};

export default TestViewer;
