import { useState } from 'react';
import TestViewer from './components/TestViewer';
import ExtractionResults from './components/ExtractionResults';
import UploadScreen from './components/UploadScreen';

function App() {
  const [selectedFile, setSelectedFile] = useState(null);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedData, setExtractedData] = useState(null);
  const [error, setError] = useState(null);
  const [showViewer, setShowViewer] = useState(false);

  // API URL configuration - use environment variable or fallback to ngrok URL
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://a0981fee51a6.ngrok-free.app';

  const uploadToBackend = async (file) => {
    setIsProcessing(true);
    setError(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(`${API_BASE_URL}/upload-pdf`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setExtractedData(result);
      console.log('Extraction results:', result);
    } catch (err) {
      console.error('Error uploading file:', err);
      setError(`Failed to process PDF: ${err.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      // Clean up previous URL if it exists
      if (selectedFile && pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPdfUrl(url);
      setShowViewer(true); // Show PDF viewer immediately after upload

      // Upload to backend for processing
      await uploadToBackend(file);
    } else {
      alert('Please select a valid PDF file');
    }
  };

  const resetToDefault = () => {
    if (selectedFile && pdfUrl) {
      URL.revokeObjectURL(pdfUrl);
    }
    setSelectedFile(null);
    setPdfUrl(null);
    setExtractedData(null);
    setError(null);
    setIsProcessing(false);
    setShowViewer(false);
  };

  // Show upload screen if no PDF is selected
  if (!showViewer) {
    return (
      <>
        <UploadScreen onFileUpload={handleFileUpload} isProcessing={isProcessing} />

        {/* Error Display */}
        {error && (
          <div className="fixed top-4 right-4 z-50 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg max-w-md">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm">{error}</span>
              <button
                onClick={() => setError(null)}
                className="ml-2 text-white hover:text-gray-200"
              >
                ×
              </button>
            </div>
          </div>
        )}
      </>
    );
  }

  // Show PDF viewer after upload
  return (
    <div className="h-screen w-screen relative">
      {/* Full Screen PDF Viewer */}
      <TestViewer pdfUrl={pdfUrl} extractedData={extractedData} />

      {/* Floating Controls */}
      <div className="fixed top-4 right-4 z-50 flex gap-2">
        {/* Upload New PDF Button */}
        <div className="relative">
          <input
            type="file"
            accept=".pdf"
            onChange={handleFileUpload}
            className="hidden"
            id="pdf-upload"
            disabled={isProcessing}
          />
          <label
            htmlFor="pdf-upload"
            className={`${isProcessing ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 cursor-pointer'} text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 shadow-lg`}
          >
            {isProcessing ? (
              <>
                <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Processing...
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                New PDF
              </>
            )}
          </label>
        </div>

        {/* Back to Upload Button */}
        <button
          onClick={resetToDefault}
          className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 shadow-lg"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back
        </button>
      </div>

      {/* File Info Overlay */}
      {selectedFile && (
        <div className="fixed bottom-4 left-4 z-50 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg">
          <div className="text-sm">
            <div className="font-semibold">{selectedFile.name}</div>
            <div className="text-gray-300">Size: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</div>
            {isProcessing && (
              <div className="text-yellow-300 mt-1 flex items-center gap-2">
                <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Processing PDF...
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="fixed top-20 right-4 z-50 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg max-w-md">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm">{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Processing Complete Notification */}
      {extractedData && !isProcessing && (
        <div className="fixed top-20 left-4 z-50 bg-green-600 text-white px-4 py-3 rounded-lg shadow-lg max-w-sm">
          <div className="flex items-center gap-3">
            <svg className="w-6 h-6 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <div className="font-semibold">Processing Complete!</div>
              <div className="text-sm text-green-200">
                Found {extractedData.total_matches} matching pairs
              </div>
            </div>
            <button
              onClick={() => setExtractedData(null)}
              className="ml-2 text-green-200 hover:text-white"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Extraction Results Display */}
      <ExtractionResults
        data={extractedData}
        onClose={() => setExtractedData(null)}
      />
    </div>
  );
}

export default App;
