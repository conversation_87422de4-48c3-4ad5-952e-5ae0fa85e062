#!/bin/bash

# Deployment script for the PDF viewer application

echo "🚀 Starting deployment process..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the project
echo "🔨 Building the project..."
npm run build

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📁 Build files are in the 'dist' directory"
    echo ""
    echo "🌐 Next steps for Netlify deployment:"
    echo "1. Go to https://app.netlify.com/"
    echo "2. Drag and drop the 'dist' folder to deploy"
    echo "3. Or connect your GitHub repository for automatic deployments"
    echo "4. Set environment variable: VITE_API_URL=https://a0981fee51a6.ngrok-free.app"
    echo ""
    echo "🔧 Backend is running at: https://a0981fee51a6.ngrok-free.app"
else
    echo "❌ Build failed!"
    exit 1
fi
