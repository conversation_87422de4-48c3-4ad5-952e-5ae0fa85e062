<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test for Netlify</h1>
    <button onclick="testCORS()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS...';
            
            try {
                const response = await fetch('https://a0981fee51a6.ngrok-free.app/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                resultDiv.innerHTML = `<p style="color: green;">✅ CORS Success!</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">❌ CORS Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
