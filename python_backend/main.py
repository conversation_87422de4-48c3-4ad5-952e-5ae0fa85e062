from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import tempfile
import os
import asyncio
from typing import List, Dict, Any
import logging

# PDF processing imports
from pdfminer.high_level import extract_pages
from pdfminer.layout import LTTextBox, LTTextLine, LTChar
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="PDF Text Extraction API", version="1.0.0")

# Configure CORS - More permissive for development

# Custom CORS middleware to handle Netlify and other origins
@app.middleware("http")
async def cors_handler(request, call_next):
    response = await call_next(request)
    origin = request.headers.get("origin")

    # Allow specific origins
    allowed_origins = [
        "http://localhost:5173",
        "http://localhost:3000",
        "http://127.0.0.1:5173",
        "https://a0981fee51a6.ngrok-free.app",
        "https://civill.netlify.app",
    ]

    # Also allow any netlify.app subdomain
    if origin and (origin in allowed_origins or origin.endswith(".netlify.app")):
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, Accept, Origin, X-Requested-With, ngrok-skip-browser-warning"
        response.headers["Access-Control-Max-Age"] = "86400"

    return response

# Also add the standard CORS middleware as backup
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://localhost:3000",
        "http://127.0.0.1:5173",
        "https://a0981fee51a6.ngrok-free.app",
        "https://civill.netlify.app",
    ],
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Global pattern for code matching
code_pattern = re.compile(r'^\d{2}\.[A-Z]\.\d{2}$')

def extract_text_and_bbox(pdf_path: str) -> List[Dict[str, Any]]:
    """
    Extract text and bounding box information from PDF.
    Returns filtered pairs matching the specified pattern.
    """
    filtered_pairs = []

    try:
        for page_layout in extract_pages(pdf_path):
            for element in page_layout:
                if isinstance(element, LTTextBox) or isinstance(element, LTTextLine):
                    text = element.get_text().strip()
                    x0, y0, x1, y1 = element.bbox

                    lines = text.strip().splitlines()
                    if len(lines) == 2 and code_pattern.match(lines[1].strip()):
                        filtered_pairs.append({
                            "label": lines[0].strip(),
                            "code": lines[1].strip(),
                            "bbox": {"x0": x0, "y0": y0, "x1": x1, "y1": y1}
                        })
                        logger.info(f"Found match: {lines[0].strip()} - {lines[1].strip()}")

    except Exception as e:
        logger.error(f"Error processing PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")

    return filtered_pairs

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "PDF Text Extraction API is running"}

@app.options("/upload-pdf")
async def upload_pdf_options():
    """Handle preflight requests for upload-pdf endpoint"""
    return {"message": "OK"}

@app.post("/upload-pdf")
async def upload_pdf(file: UploadFile = File(...)):
    """
    Upload a PDF file and extract text with bounding boxes.
    Returns filtered pairs that match the specified pattern.
    """
    # Validate file type
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")

    if not file.content_type == 'application/pdf':
        raise HTTPException(status_code=400, detail="Invalid file type. Please upload a PDF file")

    # Create temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
        try:
            # Write uploaded file to temporary location
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()

            logger.info(f"Processing PDF: {file.filename} ({len(content)} bytes)")

            # Process the PDF
            filtered_pairs = extract_text_and_bbox(temp_file.name)

            logger.info(f"Extraction complete. Found {len(filtered_pairs)} matching pairs")

            return JSONResponse(content={
                "success": True,
                "filename": file.filename,
                "total_matches": len(filtered_pairs),
                "filtered_pairs": filtered_pairs
            })

        except Exception as e:
            logger.error(f"Error processing file {file.filename}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file.name)
            except Exception as e:
                logger.warning(f"Could not delete temporary file: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)