# Deployment Guide

## Backend (Python FastAPI)

### Current Setup with Ngrok
The backend is currently accessible via ngrok at: `https://a0981fee51a6.ngrok-free.app`

### Running the Backend
```bash
cd python_backend
python main.py
```

The backend will run on `http://localhost:8000` and is forwarded through ngrok.

### CORS Configuration
The backend is configured to accept requests from:
- Local development: `http://localhost:5173`, `http://localhost:3000`
- Ngrok tunnel: `https://a0981fee51a6.ngrok-free.app`
- Netlify deployment: `https://civill.netlify.app`
- Any Netlify subdomain ending with `.netlify.app`

**CORS Issue Fixed**: The backend now includes custom CORS middleware that properly handles preflight requests and allows your Netlify domain.

## Frontend (React + Vite)

### Environment Variables
The frontend uses environment variables to configure the API URL:

- **Local Development**: Uses `.env.local` with `VITE_API_URL=http://localhost:8000`
- **Production**: Uses the ngrok URL as fallback: `https://a0981fee51a6.ngrok-free.app`

### Netlify Deployment

1. **Build the project**:
   ```bash
   npm run build
   ```

2. **Deploy to Netlify**:
   - Connect your GitHub repository to Netlify
   - Set build command: `npm run build`
   - Set publish directory: `dist`
   - Set environment variable: `VITE_API_URL=https://a0981fee51a6.ngrok-free.app`

3. **Environment Variables in Netlify**:
   Go to Site Settings > Environment Variables and add:
   ```
   VITE_API_URL=https://a0981fee51a6.ngrok-free.app
   ```

### Local Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

## Features
- PDF upload and text extraction
- Red circle plotting at x0,y0 coordinates from extracted data
- Coordinate system conversion (PDF bottom-left origin to web top-left origin)
- Toggle button to show/hide coordinate markers
- CORS-enabled for cross-origin requests

## API Endpoints
- `GET /` - Health check
- `POST /upload-pdf` - Upload PDF and extract text with bounding boxes

## Notes
- The ngrok URL (`https://a0981fee51a6.ngrok-free.app`) is temporary and will change when ngrok is restarted
- For production deployment, consider using a permanent backend hosting solution
- Update the CORS origins in `python_backend/main.py` when you have your final Netlify URL
